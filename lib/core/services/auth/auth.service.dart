import 'dart:developer';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:hrms_tst/core/services/auth/protocol/auth.protocol.dart';
import 'package:hrms_tst/core/services/network/network.service.dart';
import 'package:hrms_tst/core/utils/helpers/result.dart';
import 'package:hrms_tst/shared/models/user.model.dart';

import 'protocol/token.protocol.dart';

class AuthService implements AuthProtocol {
  final TokenProtocol tokenService;
  final NetworkService networkService;

  AuthService({required this.tokenService, required this.networkService});

  @override
  Future<Result<void>> signInWithGoogle() async {
    // await GoogleSignIn().signOut();
    try {
      final GoogleSignInAccount? account = await GoogleSignIn().signIn();
      final GoogleSignInAuthentication? authentication =
          await account?.authentication;

      final AuthCredential credential = GoogleAuthProvider.credential(
        accessToken: authentication?.accessToken,
        idToken: authentication?.idToken,
      );
      final UserCredential authResult = await FirebaseAuth.instance
          .signInWithCredential(credential);

      final user = authResult.user;

      if (user == null) return Failure('User not found');
      final token = await user.getIdToken();

      log('GOOGLE ID TOKEN: $token');

      final result = await networkService.post(
        '/public/employees/social-auth',
        data: {'firebaseToken': token},
        mapper: (response) => response,
      );

      if (result.isSuccess) {
        final response = result.success.data;
        await tokenService.setAccessToken(response.data['token']);

        return Success(null);
      } else {
        await GoogleSignIn().signOut();
        return Failure(result.failure.message);
      }
    } catch (e) {
      log(e.toString());
      await GoogleSignIn().signOut();
      return Failure('Unable to sign in with google');
    }
  }

  @override
  Future<Result<UserModel?>> getMe() async {
    return await networkService.get(
      '/employees/get-me',
      mapper: (response) {
        return UserModel.fromJson(response.data['data']['user']);
      },
    );
  }

  @override
  Future<Result<void>> logout() async {
    await tokenService.clear();
    return Success(null);
  }

  @override
  Future<Result<void>> setFCMToken(String? token) {
    return networkService.patch(
      '/users/update-fcm',
      data: {'fcm': token},
      mapper: (response) {
        return;
      },
    );
  }

  // @override
  // Future<Result<({String access, String refresh})>> refresh() async {
  //   return Success((access: '', refresh: ''));
  // }
}
