import 'dart:developer';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:hrms_tst/core/services/device_info/device_info.service.dart';
import 'package:hrms_tst/core/services/notifications/local_notifications/local_notifications.service.dart';
import 'package:hrms_tst/core/services/permissions/permissions.service.dart';
import '../local_notifications/local_notifications.protocol.dart';

@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  log('Handling a background message: ${message.data}');

  final flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();
  final permissions = PermissionsService(
    deviceInfoService: DeviceInfoService(deviceInfoPlugin: DeviceInfoPlugin()),
  ); // Assuming you have a way to get this

  final localNotificationsService = LocalNotificationsService(
    flutterLocalNotificationsPlugin: flutterLocalNotificationsPlugin,
    permissions: permissions,
  );

  localNotificationsService.showNotification(
    id: message.notification?.hashCode ?? 0,
    title: message.notification?.title ?? 'No title',
    body: message.notification?.body ?? 'No body',
  );
}

class PushNotificationsService {
  PushNotificationsService({
    required LocalNotificationsProtocol localNotificationsService,
  }) : _localNotificationsService = localNotificationsService;

  final FirebaseMessaging _messaging = FirebaseMessaging.instance;
  final LocalNotificationsProtocol _localNotificationsService;

  Future<String?> get token async => await _messaging.getToken();

  Future<void> init() async {
    await _messaging.requestPermission(alert: true, badge: true, sound: true);

    log('FCM Token: ${await token}');

    // Listen for foreground messages
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      final notification = message.notification;
      final android = message.notification?.android;

      if (notification != null && android != null) {
        _localNotificationsService.showNotification(
          id: notification.hashCode,
          title: notification.title ?? 'No title',
          body: notification.body ?? 'No body',
        );
      }
    });

    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      log('Notification clicked with data: ${message.data}');
    });

    final initialMessage = await _messaging.getInitialMessage();
    if (initialMessage != null) {
      log(
        'App launched from terminated state via notification: ${initialMessage.data}',
      );
    }

    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
  }
}
