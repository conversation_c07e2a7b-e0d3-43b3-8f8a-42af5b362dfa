import 'dart:developer';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:hrms_tst/core/config/environment.dart';
import 'package:hrms_tst/core/services/auth/token.service.dart';
import 'package:hrms_tst/core/services/device_info/device_info.service.dart';
import 'package:hrms_tst/core/services/network/network.service.dart';
import 'package:hrms_tst/core/services/notifications/local_notifications/local_notifications.service.dart';
import 'package:hrms_tst/core/services/permissions/permissions.protocol.dart';
import 'package:hrms_tst/core/services/permissions/permissions.service.dart';
import 'package:hrms_tst/core/services/prefs/prefs.service.dart';
import 'package:hrms_tst/core/utils/helpers/result.dart';
import 'package:hrms_tst/dependencies.dart';
import 'package:intl/intl.dart';
import 'package:native_geofence/native_geofence.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:talker_dio_logger/talker_dio_logger.dart';

@pragma('vm:entry-point')
Future<void> geofenceTriggered(GeofenceCallbackParams params) async {
  log('Geofence triggered : ${params.toString()}');

  final env = Environment.dev;

  final deviceInfoService = DeviceInfoService(
    deviceInfoPlugin: DeviceInfoPlugin(),
  );

  final sharedPrefs = await SharedPreferences.getInstance();
  final prefsService = PrefsService(prefs: sharedPrefs);
  final tokenService = TokenService(prefs: prefsService);
  final accessToken = await tokenService.accessToken;

  if (accessToken == null) {
    log('Authentication token is null, cannot proceed with geofence action');
    return;
  }

  if (params.event == GeofenceEvent.enter) {
    final networkService = NetworkService(dio: Dio(), env: env);

    await networkService.addInterceptor(
      (networkService) => TalkerDioLogger(settings: TalkerDioLoggerSettings()),
    );

    log('User Entered Office Location Trigger Clock In');

    final geofence = params.geofences.first;
    final clockInResult = await networkService.post(
      '/attendances/auto-clock-in',
      options: Options(
        headers: {'Authorization': 'Bearer ${await tokenService.accessToken}'},
      ),
      data: {
        "lat": params.location?.latitude ?? geofence.location.latitude,
        "long": params.location?.longitude ?? geofence.location.longitude,
      },
      mapper: (response) {},
    );

    await clockInResult.fold(
      onSuccess: (data) async {
        final plugin = FlutterLocalNotificationsPlugin();
        final localNotificationService = LocalNotificationsService(
          flutterLocalNotificationsPlugin: plugin,
          permissions: PermissionsService(deviceInfoService: deviceInfoService),
        );
        await localNotificationService.init();
        await localNotificationService.showNotification(
          id: 0,
          title: 'Clocked In',
          body:
              'You’ve clocked in successfully at ${DateFormat('hh:mm a').format(DateTime.now())}', // e.g., "entered" or "exited"
          payload: null,
        );
      },
      onFailure: (message) {
        log('Failed to clock in: $message');
      },
    );
  } else {
    log('Geofence Event: ${params.event}');
  }
}

class GeofencingService {
  final permissionService = getIt.get<PermissionsProtocol>();

  final _manager = NativeGeofenceManager.instance;

  Future<Result<void>> init({required Function(String message) onError}) async {
    final available = await permissionService.requestGeofencePermission(
      onLocationDenied: () {
        onError('Location Permission Denied');
      },
      onLocationAlwaysDenied: () {
        onError('Geofence Tracking Permission Denied');
      },
    );

    if (!available) {
      return Failure('Unable to initialize geofencing');
    }

    await _manager.initialize();

    log('Geofencing service initialized successfully');

    return Success(null);
  }

  Future<Result<List<ActiveGeofence>>> getGeofences() async {
    return await _manager.getRegisteredGeofences().asResult();
  }

  Future<Result<void>> reCreateAfterReboot() async {
    return await _manager.reCreateAfterReboot().asResult();
  }

  Future<Result<void>> addGeofence({
    required String id,
    required double latitude,
    required double longitude,
    required double radiusInMeters,
  }) async {
    log('Add geofence : $id');
    return await _manager
        .createGeofence(
          Geofence(
            id: id,
            location: Location(latitude: latitude, longitude: longitude),
            radiusMeters: radiusInMeters,
            triggers: {GeofenceEvent.enter},
            iosSettings: IosGeofenceSettings(),
            androidSettings: AndroidGeofenceSettings(
              initialTriggers: {GeofenceEvent.enter},
            ),
          ),
          geofenceTriggered,
        )
        .asResult();
  }

  Future<Result<void>> removeGeofence({required String id}) async {
    return await _manager.removeGeofenceById(id).asResult();
  }

  Future<Result<void>> removeAllGeofences() async {
    return await _manager.removeAllGeofences().asResult();
  }
}

// Future<void> showLocalNotificationFromIsolate(
//   FlutterLocalNotificationsPlugin plugin,
//   int id,
//   String title,
//   String body,
//   Map<String, dynamic>? payload,
// ) async {
//   const AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
//     'geofence_channel',
//     'Geofence Notifications',
//     importance: Importance.max,
//     priority: Priority.high,
//   );

//   const NotificationDetails notificationDetails = NotificationDetails(
//     android: androidDetails,
//     iOS: DarwinNotificationDetails(
//       presentAlert: true,
//       presentBadge: true,
//       presentSound: true,
//     ),
//   );

//   await plugin.show(
//     id,
//     title,
//     body, // e.g., "entered" or "exited"
//     payload: jsonEncode(payload),
//     notificationDetails,
//   );
// }

// Future<void> setupLocalNotificationForIsolate(
//   FlutterLocalNotificationsPlugin plugin,
// ) async {
//   await plugin
//       .resolvePlatformSpecificImplementation<
//         IOSFlutterLocalNotificationsPlugin
//       >()
//       ?.requestPermissions(alert: true, badge: true, sound: true);

//   const AndroidInitializationSettings androidSettings =
//       AndroidInitializationSettings('@mipmap/ic_launcher');

//   const InitializationSettings settings = InitializationSettings(
//     android: androidSettings,
//     iOS: DarwinInitializationSettings(),
//   );

//   await plugin.initialize(settings);
// }
