import 'dart:typed_data';
import 'package:image/image.dart' as img;

import 'package:tflite_flutter/tflite_flutter.dart';

class FaceRecognizationService {
  late final Interpreter _interpreter;

  Future<void> loadModel() async {
    try {
      _interpreter = await Interpreter.fromAsset(
        'assets/ml-model/mobilefacenet.tflite',
      );
    } catch (e) {
      throw Exception('Failed to load model: $e');
    }
  }

  Future<void> dispose() async {
    if (_interpreter.isDeleted) return;
    _interpreter.close();
  }

  Future<List<double>> getEmbedding(img.Image faceImage) async {
    final input = imageToInput(faceImage);
    final output = List.filled(192, 0.0).reshape([1, 192]);

    _interpreter.run(input.reshape([1, 112, 112, 3]), output);
    return List<double>.from(output[0]);
  }

  Float32List imageToInput(img.Image image) {
    final resized = img.resize(image, width: 112, height: 112);
    final input = Float32List(1 * 112 * 112 * 3);
    final buffer = input.buffer.asFloat32List();

    int pixelIndex = 0;
    for (int y = 0; y < 112; y++) {
      for (int x = 0; x < 112; x++) {
        final pixel = resized.getPixel(x, y);
        final r = pixel.r;
        final g = pixel.g;
        final b = pixel.b;

        // Normalize to [-1, 1]
        buffer[pixelIndex++] = (r / 255.0 - 0.5) * 2.0;
        buffer[pixelIndex++] = (g / 255.0 - 0.5) * 2.0;
        buffer[pixelIndex++] = (b / 255.0 - 0.5) * 2.0;
      }
    }

    return input;
  }
}
