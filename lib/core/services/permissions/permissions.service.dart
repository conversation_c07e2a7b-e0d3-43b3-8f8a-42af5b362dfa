import 'dart:developer';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:permission_handler/permission_handler.dart';

import '../device_info/device_info.service.dart';
import 'permissions.protocol.dart';

class PermissionsService implements PermissionsProtocol {
  PermissionsService({required DeviceInfoService deviceInfoService})
    : _deviceInfo = deviceInfoService.plugin;

  final DeviceInfoPlugin _deviceInfo;

  @override
  Future<bool> requestNotificationsPermission({
    Function? onDenied,
    Function? onPermanentlyDenied,
  }) {
    return _requestPermission(
      permission: Permission.notification,
      onDenied: onDenied,
      onPermanentlyDenied: onPermanentlyDenied,
    );
  }

  @override
  Future<bool> requestImagePermission({
    Function? onDenied,
    Function? onPermanentlyDenied,
  }) async {
    final permission = Platform.isAndroid
        ? (await _deviceInfo.androidInfo).version.sdkInt >= 33
              ? Permission.photos
              : Permission.storage
        : Permission.photos;

    return await _requestPermission(
      permission: permission,
      onDenied: onDenied,
      onPermanentlyDenied: onPermanentlyDenied,
    );
  }

  @override
  Future<bool> requestGeofencePermission({
    Function? onLocationDenied,
    Function? onLocationAlwaysDenied,
  }) async {
    final locationPermission = await _requestPermission(
      permission: Permission.location,
      onDenied: onLocationDenied,
      onPermanentlyDenied: onLocationDenied,
    );

    if (locationPermission) {
      final locationAlwaysStatus = await Permission.locationAlways.request();

      if (!locationAlwaysStatus.isGranted) {
        onLocationAlwaysDenied?.call();
        return false;
      } else {
        return true;
      }
    } else {
      onLocationDenied?.call();
      return false;
    }
  }

  Future<bool> _requestPermission({
    required Permission permission,
    Function? onDenied,
    Function? onPermanentlyDenied,
  }) async {
    final status = await permission.request();

    log('Permission ${permission.toString()} status: $status');

    if (!status.isGranted) {
      if (status.isPermanentlyDenied) {
        onPermanentlyDenied?.call();
        return false;
      } else {
        onDenied?.call();
        return false;
      }
    } else {
      return true;
    }
  }
}
