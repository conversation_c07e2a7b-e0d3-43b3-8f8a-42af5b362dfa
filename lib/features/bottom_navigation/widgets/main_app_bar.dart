import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:hrms_tst/features/face_scanner/presentation/face_recognition_home.screen.dart';
import 'package:hrms_tst/features/teammates/domain/edit_profile.controller.dart';
import 'package:hrms_tst/features/teammates/domain/teammates.controller.dart';
import 'package:hrms_tst/features/teammates/presentation/profile.screen.dart';
import 'package:hrms_tst/features/teammates/presentation/teammate_detail.screen.dart';
import 'package:hrms_tst/gen/assets.gen.dart';
import 'package:hrms_tst/shared/widgets/app_text_field.dart';
import 'package:hrms_tst/shared/widgets/cached_image.dart';
import 'package:hrms_tst/shared/widgets/svg_image.dart';

import '../../teammates/data/models/teammate.model.dart';

class MainAppBar extends HookConsumerWidget implements PreferredSizeWidget {
  const MainAppBar({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final user = ref.watch(profileDataProvider);

    final isSearching = useState(false);

    final searchQuery = useState('');

    return AppBar(
      toolbarHeight: 70,
      scrolledUnderElevation: 0,
      title: AnimatedSwitcher(
        switchInCurve: Curves.easeIn,
        switchOutCurve: Curves.easeOut,
        duration: Duration(milliseconds: 300),
        transitionBuilder: (child, animation) =>
            FadeTransition(opacity: animation, child: child),
        child: isSearching.value
            ? Row(
                children: [
                  Expanded(
                    child: TypeAheadField<TeammateModel>(
                      builder: (context, controller, focusNode) {
                        return AppTextField(
                          autofocus: true,
                          controller: controller,
                          focusNode: focusNode,
                          contentPadding: EdgeInsets.symmetric(
                            vertical: 12,
                            horizontal: 16,
                          ),
                          hintText: 'Search Teammates',
                          onChanged: (value) => searchQuery.value = value,
                        );
                      },

                      itemBuilder: (context, value) => ListTile(
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 0,
                        ),
                        leading: CachedImage(
                          url: value.user.image ?? '',
                          height: 44,
                          width: 44,
                          decoration: BoxDecoration(shape: BoxShape.circle),
                        ),
                        title: Text(value.user.displayName ?? ''),
                        subtitle: Text(
                          value.jobRoles.map((e) => e.name).join(', '),
                        ),
                      ),

                      onSelected: (value) {
                        FocusScope.of(context).unfocus();
                        context.goNamed(
                          TeammateDetailScreen.name,
                          pathParameters: {'id': value.user.id.toString()},
                        );
                      },
                      suggestionsCallback: (search) async {
                        if (search.trim().isEmpty) return [];
                        final teammates = await ref.read(
                          teammatesByQueryProvider(search).future,
                        );
                        return teammates;
                      },
                      emptyBuilder: (context) {
                        return Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Text('No Teammates Found'),
                        );
                      },
                      hideOnEmpty: searchQuery.value.isEmpty,
                      constraints: BoxConstraints(
                        maxHeight: context.screenHeight * 0.4,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () {
                      searchQuery.value = '';
                      isSearching.value = false;
                    },
                    icon: Icon(Icons.cancel, color: context.colors.secondary),
                  ),
                ],
              )
            : Row(
                children: [
                  Space.x(4),
                  SVGImage(
                    Assets.svgs.tstTechnologyFullName,
                    height: 30,
                    applyColor: false,
                  ),
                ],
              ),
      ),
      actions: isSearching.value
          ? []
          : [
              IconButton(
                icon: SVGImage(
                  Assets.svgs.searchIcon,
                  color: context.colors.secondary,
                  height: 28,
                ),
                onPressed: () {
                  isSearching.value = true;
                },
              ),
              IconButton(
                icon: SVGImage(
                  Assets.svgs.notificationsIcon,
                  color: context.colors.secondary,
                  height: 28,
                ),
                onPressed: () async {
                  context.pushNamed(FaceRecognitionHomeScreen.name);
                },
              ),
              Space.x(8),
              GestureDetector(
                onTap: () {
                  context.pushNamed(ProfileScreen.name);
                },
                child: CircleAvatar(
                  radius: 15,
                  backgroundColor: context.colors.secondary,
                  child: CircleAvatar(
                    backgroundColor: Colors.transparent,
                    radius: 14,
                    child: CachedImage(
                      url: user.image ?? '',
                      decoration: BoxDecoration(shape: BoxShape.circle),
                    ),
                  ),
                ),
              ),
              Space.x(16),
            ],
    );
  }

  @override
  Size get preferredSize => Size(double.infinity, 70);
}
