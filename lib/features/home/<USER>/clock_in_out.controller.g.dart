// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'clock_in_out.controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$clockInOutHash() => r'dbe8909e73ee20c4e79477cb9fa782bad677a951';

/// See also [ClockInOut].
@ProviderFor(ClockInOut)
final clockInOutProvider =
    AutoDisposeAsyncNotifierProvider<ClockInOut, LastClockInModel>.internal(
      ClockInOut.new,
      name: r'clockInOutProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$clockInOutHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$ClockInOut = AutoDisposeAsyncNotifier<LastClockInModel>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
