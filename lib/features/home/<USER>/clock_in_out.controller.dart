import 'dart:developer';

import 'package:geolocator/geolocator.dart';
import 'package:hrms_tst/core/services/geofencing/geofencing.service.dart';
import 'package:hrms_tst/core/utils/helpers/result.dart';
import 'package:hrms_tst/dependencies.dart';
import 'package:hrms_tst/features/home/<USER>/models/clock_in.model.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../data/home.protocol.dart';
import '../data/models/work_update.model.dart';

part 'clock_in_out.controller.g.dart';

@riverpod
class ClockInOut extends _$ClockInOut {
  @override
  FutureOr<LastClockInModel> build() async {
    return (await getIt.get<HomeProtocol>().getLastClockIn()).ignoreFailure();
  }

  Future<Result<String?>> clockIn() async {
    final fallbackState = state.requireValue;
    state = AsyncLoading();
    final position = await getLocation(
      onError: (message) {
        state = AsyncData(fallbackState);
        return Failure(message);
      },
    );

    if (position == null) {
      state = AsyncData(fallbackState);
      return Failure('Unable to get location');
    }

    final result = await getIt.get<HomeProtocol>().clockIn(
      lat: position.latitude,
      long: position.longitude,
    );

    result.fold(
      onSuccess: (data) => ref.invalidateSelf(),
      onFailure: (message) {
        state = AsyncData(fallbackState);
        return Failure(message);
      },
    );

    return result;
  }

  Future<Result<void>> clockOut({
    required List<WorkUpdateModel> workUpdates,
  }) async {
    final result = await getIt.get<HomeProtocol>().clockOut(
      workUpdates: workUpdates,
    );
    if (result.isSuccess) {
      ref.invalidateSelf();
    }

    return result;
  }

  Future<Position?> getLocation({
    required Function(String message) onError,
  }) async {
    LocationPermission permission;

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        // Permissions are denied, next time you could try
        // requesting permissions again (this is also where
        // Android's shouldShowRequestPermissionRationale
        // returned true. According to Android guidelines
        // your App should show an explanatory UI now.
        onError('Location permissions are denied');
        return null;
      }
    }

    if (permission == LocationPermission.deniedForever) {
      // Permissions are denied forever, handle appropriately.
      onError(
        'Location permissions are permanently denied, we cannot request permissions.',
      );
      return null;
    }

    // When we reach here, permissions are granted and we can
    // continue accessing the position of the device.
    try {
      return await Geolocator.getCurrentPosition();
    } catch (e) {
      return null;
    }
  }

  Future<void> setupGeofence({
    required Function(String message) onError,
  }) async {
    final geofence = getIt.get<GeofencingService>();
    await geofence.init(onError: onError);

    final addedGeofences = await geofence.getGeofences();
    await addedGeofences.fold(
      onSuccess: (fences) async {
        final isAlreadyAdded = fences.map((e) => e.id).contains('surat-office');
        if (isAlreadyAdded) {
          await geofence.reCreateAfterReboot();
        } else {
          await geofence.addGeofence(
            id: 'surat-office',
            latitude: 21.235,
            longitude: 72.871,
            radiusInMeters: 100,
          );
        }
      },
      onFailure: (message) => log(message),
    );
  }
}
