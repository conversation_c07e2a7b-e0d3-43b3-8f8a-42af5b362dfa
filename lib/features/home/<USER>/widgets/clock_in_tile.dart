import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/core/utils/helpers/result.dart';
import 'package:hrms_tst/core/utils/helpers/snackbar.helper.dart';
import 'package:hrms_tst/core/utils/helpers/space.dart';
import 'package:hrms_tst/features/home/<USER>/clock_in_out.controller.dart';
import 'package:hrms_tst/features/home/<USER>/work_update.screen.dart';
import 'package:hrms_tst/features/teammates/domain/edit_profile.controller.dart';
import 'package:hrms_tst/shared/widgets/card_container.dart';
import 'package:hrms_tst/shared/widgets/confirmation_dialog.dart';
import 'package:hrms_tst/shared/widgets/loading_placeholder.dart';
import 'package:intl/intl.dart';

import 'package:permission_handler/permission_handler.dart';

class ClockInTile extends ConsumerWidget {
  const ClockInTile({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final user = ref.watch(profileDataProvider);

    final clockIn = ref.watch(clockInOutProvider);
    return CardContainer(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            HookBuilder(
              builder: (context) {
                final time = useState(DateTime.now().toLocal());

                useEffect(() {
                  final timer = Timer.periodic(Duration(seconds: 1), (timer) {
                    time.value = DateTime.now().toLocal();
                  });
                  return () => timer.cancel();
                }, []);
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    switch (clockIn) {
                      AsyncData(:final value) => Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _timeSection(
                            context,
                            'Last Clock ${value.clockOut == null ? 'In' : 'Out'}',
                            DateFormat('hh:mm a').format(
                              (value.clockOut ?? value.clockIn!).toLocal(),
                            ),
                          ),

                          Expanded(
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Space.x(24),
                                if (value.clockOut == null)
                                  _timeSection(
                                    context,
                                    'Working Hours',
                                    '${(time.value.difference(value.clockIn!.toLocal()).inHours.toString().padLeft(2, '0'))}:${(time.value.difference(value.clockIn!.toLocal()).inMinutes % 60).toString().padLeft(2, '0')}',
                                  ),
                                Spacer(),
                                Text(
                                  DateFormat('hh:mm a').format(time.value),
                                  style: context.textStyles.bodySmall?.copyWith(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w600,
                                    color: context.colors.secondary,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      AsyncError(:final error) => Text(error.toString()),

                      _ => Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              LoadingPlaceholder(height: 11, width: 70),
                              Space.y(2),
                              LoadingPlaceholder(height: 14, width: 70),
                            ],
                          ),
                          Space.x(24),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              LoadingPlaceholder(height: 11, width: 70),
                              Space.y(2),
                              LoadingPlaceholder(height: 14, width: 70),
                            ],
                          ),
                          Spacer(),
                          LoadingPlaceholder(height: 16, width: 80),
                        ],
                      ),
                    },

                    Space.y(18),
                    Text(
                      'Hi ${user.firstName}, ${greetingText(time.value)}',
                      style: context.textStyles.titleLarge?.copyWith(
                        fontSize: 24,
                        fontWeight: FontWeight.w500,
                        color: context.colors.secondary,
                      ),
                    ),
                  ],
                );
              },
            ),
            Space.y(18),
            clockIn.when(
              skipLoadingOnReload: false,
              data: (value) => ElevatedButton(
                onPressed: () async {
                  if (value.clockOut == null) {
                    context.pushNamed(WorkUpdateScreen.name);
                    // await ref.read(clockInProvider.notifier).clockOut();
                  } else {
                    showAdaptiveDialog(
                      context: context,
                      builder: (dialogContext) => ConfirmationDialog(
                        title: 'Are you sure you want to Clock In?',
                        confirmText: 'Yes',
                        onConfirm: () async {
                          (await ref
                                  .read(clockInOutProvider.notifier)
                                  .clockIn())
                              .fold(
                                onSuccess: (lateMessage) {
                                  if (lateMessage != null) {
                                    showAdaptiveDialog(
                                      context: context,
                                      builder: (context) {
                                        return AlertDialog.adaptive(
                                          title: Text('Late Clock In'),
                                          content: Text(lateMessage),
                                          actions: [
                                            TextButton(
                                              style: Platform.isIOS
                                                  ? TextButton.styleFrom(
                                                      elevation: 0,
                                                      splashFactory: NoSplash
                                                          .splashFactory,
                                                      overlayColor:
                                                          Colors.transparent,
                                                    )
                                                  : TextButton.styleFrom(
                                                      backgroundColor: context
                                                          .colors
                                                          .outline,
                                                    ),
                                              onPressed: () {
                                                context.pop();
                                              },
                                              child: Text('Okay'),
                                            ),
                                          ],
                                        );
                                      },
                                    );
                                  }
                                  context.showSuccess(
                                    'Clocked In Successfully',
                                  );
                                },
                                onFailure: (message) {
                                  context.showError(
                                    message,
                                    message !=
                                            'Can\'t clock in when you are on leave'
                                        ? SnackBarAction(
                                            label: 'Grant',
                                            backgroundColor:
                                                context.colors.surface,
                                            textColor: context.colors.onSurface,
                                            onPressed: () {
                                              openAppSettings();
                                            },
                                          )
                                        : null,
                                  );
                                },
                              );
                        },
                      ),
                    );
                  }
                },
                style: ElevatedButton.styleFrom(
                  padding: EdgeInsets.symmetric(vertical: 12),
                  shape: ContinuousRectangleBorder(
                    borderRadius: BorderRadiusGeometry.circular(24),
                  ),
                  backgroundColor: value.clockOut == null
                      ? context.colors.error
                      : context.colors.primary,
                ),
                child: Text(
                  'Clock ${value.clockOut == null ? 'Out' : 'In'}',
                  style: context.textStyles.bodyLarge?.copyWith(
                    fontSize: 17,
                    fontWeight: FontWeight.w500,
                    color: context.colors.surface,
                  ),
                ),
              ),
              error: (error, stackTrace) => Text(error.toString()),
              loading: () => ElevatedButton(
                onPressed: () {},
                style: ElevatedButton.styleFrom(
                  padding: EdgeInsets.symmetric(vertical: 12),
                  shape: ContinuousRectangleBorder(
                    borderRadius: BorderRadiusGeometry.circular(24),
                  ),
                ),
                child: Center(
                  child: SizedBox.square(
                    dimension: 25,
                    child: CircularProgressIndicator.adaptive(
                      valueColor: AlwaysStoppedAnimation(
                        context.colors.onPrimary,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String greetingText(DateTime time) {
    return switch (time.hour) {
      >= 6 && < 12 => 'Good Morning',
      >= 12 && < 17 => 'Good Afternoon',
      >= 17 && < 21 => 'Good Evening',
      >= 21 || < 6 => 'Good Night',
      _ => 'Good Day',
    };
  }

  Widget _timeSection(BuildContext context, String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: context.textStyles.bodySmall?.copyWith(
            fontSize: 8,
            color: context.colors.secondary,
          ),
        ),
        Text(
          value,
          style: context.textStyles.bodySmall?.copyWith(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: context.colors.secondary,
          ),
        ),
      ],
    );
  }
}
