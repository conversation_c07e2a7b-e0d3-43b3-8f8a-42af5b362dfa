import 'dart:io';

import 'package:face_camera/face_camera.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/features/face_scanner/domain/face_recognizer.controller.dart';

class FaceScanScreen extends HookConsumerWidget {
  const FaceScanScreen({super.key});

  static const String name = 'Face Scanner';
  static const String path = '/face-scanner';

  static GoRoute route({List<RouteBase> routes = const []}) => GoRoute(
    path: path,
    name: name,
    routes: routes,
    pageBuilder: (context, state) =>
        const NoTransitionPage(child: FaceScanScreen()),
  );

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(faceRecognizerProvider);

    final image = useState<File?>(null);

    return image.value != null
        ? Center(
            child: Stack(
              alignment: Alignment.bottomCenter,
              children: [
                Image.file(
                  image.value!,
                  width: double.maxFinite,
                  fit: BoxFit.fitWidth,
                ),
              ],
            ),
          )
        : Scaffold(
            body: switch (state) {
              AsyncData<FaceCameraController> controller => SmartFaceCamera(
                controller: controller.value,
              ),
              AsyncError(:final error) => Center(child: Text('Error: $error')),
              _ => const Center(child: CircularProgressIndicator()),
            },
          );
  }
}
