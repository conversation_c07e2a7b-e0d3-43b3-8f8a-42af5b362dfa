// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'face_registration.controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$faceRegistrationHash() => r'499a1bb5edfe6fecc450300b8c7fcb24584e7f05';

/// See also [FaceRegistration].
@ProviderFor(FaceRegistration)
final faceRegistrationProvider =
    AutoDisposeAsyncNotifierProvider<
      FaceRegistration,
      FaceCameraController
    >.internal(
      FaceRegistration.new,
      name: r'faceRegistrationProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$faceRegistrationHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$FaceRegistration = AutoDisposeAsyncNotifier<FaceCameraController>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
