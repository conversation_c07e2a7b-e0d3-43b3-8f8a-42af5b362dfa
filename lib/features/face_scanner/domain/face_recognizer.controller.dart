import 'dart:io';
import 'dart:math';

import 'dart:developer' as dev;

import 'package:face_camera/face_camera.dart';
import 'package:hrms_tst/core/services/face_recognization/face_recognization.service.dart';
import 'package:hrms_tst/features/face_scanner/domain/face_registration.controller.dart';
import 'package:image/image.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'face_recognizer.controller.g.dart';

enum FaceRecognizerStatus { idle, processing, success, error }

@riverpod
class FaceRecognizer extends _$FaceRecognizer {
  final faceService = FaceRecognizationService();

  FaceRecognizerStatus _status = FaceRecognizerStatus.idle;
  String? _message;
  bool _isProcessing = false;

  FaceRecognizerStatus get status => _status;
  String? get message => _message;

  DateTime? _lastProcessTime;

  @override
  FutureOr<FaceCameraController> build() async {
    _resetState();
    await faceService.loadModel();

    final controller = FaceCameraController(
      defaultCameraLens: CameraLens.front,
      performanceMode: FaceDetectorMode.accurate,
      enableAudio: false,
      onCapture: (file) async {},
      onFaceDetected: _handleFaceDetected,
    );

    ref.onDispose(() async {
      await faceService.dispose();
      await controller.dispose();
    });

    return controller;
  }

  Future<void> _handleFaceDetected(Face? face) async {
    if (face == null || _isProcessing) return;

    final now = DateTime.now();
    if (_lastProcessTime != null &&
        now.difference(_lastProcessTime!) < const Duration(seconds: 2)) {
      return;
    }

    _isProcessing = true;
    _lastProcessTime = now;
    _updateState(
      status: FaceRecognizerStatus.processing,
      message: 'Stay still, capturing...',
    );

    try {
      // Trigger auto-capture when face is detected
      final file = await state.requireValue.takePicture();
      if (file == null) {
        await _handleError('No face detected, please try again.');
        return;
      }
      // Get the captured image and crop it using the detected face bounds
      final fileData = await File(file.path).readAsBytes();
      final image = decodeImage(fileData);
      if (image == null) {
        await _handleError('Failed to process image');
        return;
      }

      // Crop to face region with padding
      final rect = face.boundingBox;
      final padding = (rect.width * 0.2).round();
      final cropX = max(0, rect.left.round() - padding);
      final cropY = max(0, rect.top.round() - padding);
      final cropWidth = min(
        image.width - cropX,
        rect.width.round() + padding * 2,
      );
      final cropHeight = min(
        image.height - cropY,
        rect.height.round() + padding * 2,
      );

      final croppedFace = copyCrop(
        image,
        x: cropX,
        y: cropY,
        width: cropWidth,
        height: cropHeight,
      );

      await _processImage(croppedFace);
    } catch (e) {
      await _handleError('Failed to capture image: $e');
    }
  }

  Future<void> _processImage(Image croppedFace) async {
    try {
      final image = croppedFace;

      _updateState(
        status: FaceRecognizerStatus.processing,
        message: 'Verifying your identity...',
      );

      final embedding = await faceService.getEmbedding(image);
      final result = await verifyEmbedding(embedding);

      if (result) {
        _updateState(
          status: FaceRecognizerStatus.success,
          message: 'Face Verified',
        );
      } else {
        _updateState(
          status: FaceRecognizerStatus.error,
          message: 'Face Verification Failed',
        );
      }

      // Wait and reset state based on response
      await Future.delayed(Duration(seconds: result ? 3 : 2));
      _resetState();
    } catch (e) {
      await _handleError('An error occurred: $e');
    }
  }

  Future<void> _handleError(String message) async {
    _updateState(status: FaceRecognizerStatus.error, message: message);
    await Future.delayed(const Duration(seconds: 2));
    _resetState();
  }

  void _updateState({
    required FaceRecognizerStatus status,
    required String message,
  }) {
    _status = status;
    _message = message;
    ref.notifyListeners();
  }

  void _resetState() {
    _status = FaceRecognizerStatus.idle;
    _message = null;
    _isProcessing = false;
    _lastProcessTime = null;
    ref.notifyListeners();
  }

  Future<bool> verifyEmbedding(List<double> embedding) async {
    final embeddings = ref.read(embeddingsProvider);

    final matches = embeddings.indexed.map((e) {
      final score = _cosineSimilarity(embedding, e.$2);
      return {'id': e.$1, 'score': score};
    }).toList();

    matches.sort(
      (a, b) => (b['score'] as double).compareTo(a['score'] as double),
    );

    // Best match
    final best = matches.first;

    dev.log(
      'Best match: ${best['id']} with score: ${best['score']}',
      name: 'FaceRecognizer',
    );

    if ((best['score'] as double) > 0.85) {
      return true;
    } else {
      return false;
    }
  }

  double _cosineSimilarity(List<double> a, List<double> b) {
    if (a.length != b.length) {
      throw Exception('Vectors must be of same length');
    }

    double dot = 0.0;
    double normA = 0.0;
    double normB = 0.0;

    for (int i = 0; i < a.length; i++) {
      dot += a[i] * b[i];
      normA += a[i] * a[i];
      normB += b[i] * b[i];
    }

    // Normalize to [0, 1] range
    final cosine = dot / (sqrt(normA) * sqrt(normB));
    return (cosine + 1) / 2;
  }
}
