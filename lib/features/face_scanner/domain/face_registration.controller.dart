import 'dart:developer';
import 'dart:io';
import 'dart:math' hide log;

import 'package:face_camera/face_camera.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/services/face_recognization/face_recognization.service.dart';
import 'package:image/image.dart' as img;
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'face_registration.controller.g.dart';

enum FaceRegistrationStatus { idle, processing, success, error }

final embeddingsProvider = StateProvider<List<List<double>>>((ref) {
  return [];
});

@riverpod
class FaceRegistration extends _$FaceRegistration {
  final faceService = FaceRecognizationService();

  late final FaceCameraController _cameraController;
  FaceRegistrationStatus _status = FaceRegistrationStatus.idle;
  String? _message;
  bool _isProcessing = false;
  DateTime? _lastProcessTime;

  final employeeIdController = TextEditingController();

  FaceRegistrationStatus get status => _status;
  String? get message => _message;
  bool get isRegistering => _isProcessing;

  @override
  FutureOr<FaceCameraController> build() async {
    _resetState();
    await faceService.loadModel();

    _cameraController = FaceCameraController(
      defaultCameraLens: CameraLens.front,
      performanceMode: FaceDetectorMode.accurate,
      enableAudio: false,
      onCapture: _handleCapture,
      onFaceDetected: _handleFaceDetected,
    );

    ref.onDispose(() {
      employeeIdController.dispose();
      _cameraController.dispose();
      faceService.dispose();
    });

    return _cameraController;
  }

  void startRegistration() {
    if (employeeIdController.text.trim().isEmpty) {
      _updateState(
        status: FaceRegistrationStatus.error,
        message: 'Please enter an Employee ID',
      );
      return;
    }

    _updateState(
      status: FaceRegistrationStatus.processing,
      message: 'Looking for face...',
    );
  }

  Future<void> _handleFaceDetected(Face? face) async {
    if (face == null ||
        _isProcessing ||
        status != FaceRegistrationStatus.processing) {
      return;
    }

    // Prevent too frequent captures
    final now = DateTime.now();
    if (_lastProcessTime != null &&
        now.difference(_lastProcessTime!) < const Duration(seconds: 2)) {
      return;
    }

    _isProcessing = true;
    _lastProcessTime = now;

    try {
      _updateState(
        status: FaceRegistrationStatus.processing,
        message: 'Stay still, capturing...',
      );

      // Pause detection and capture image
      await _cameraController.stopImageStream();
      final file = await _cameraController.takePicture();

      if (file == null) {
        await _handleError('Failed to capture image');
        return;
      }

      // Get the captured image and crop it using the detected face bounds
      final fileData = await File(file.path).readAsBytes();
      final image = img.decodeImage(fileData);
      if (image == null) {
        await _handleError('Failed to process image');
        return;
      }

      // Crop to face region with padding
      final rect = face.boundingBox;
      final padding = (rect.width * 0.2).round();
      final cropX = max(0, rect.left.round() - padding);
      final cropY = max(0, rect.top.round() - padding);
      final cropWidth = min(
        image.width - cropX,
        rect.width.round() + padding * 2,
      );
      final cropHeight = min(
        image.height - cropY,
        rect.height.round() + padding * 2,
      );

      final croppedFace = img.copyCrop(
        image,
        x: cropX,
        y: cropY,
        width: cropWidth,
        height: cropHeight,
      );

      await _processImage(croppedFace);
    } catch (e) {
      await _handleError('Failed to capture image: $e');
    }
  }

  Future<void> _handleCapture(File? file) async {
    if (file == null) return;

    final fileData = await file.readAsBytes();
    final image = img.decodeImage(fileData);
    if (image == null) {
      await _handleError('Failed to process image');
      return;
    }
    await _processImage(image);
  }

  Future<void> _processImage(img.Image faceImage) async {
    try {
      _updateState(
        status: FaceRegistrationStatus.processing,
        message: 'Processing face data...',
      );

      final embedding = await faceService.getEmbedding(faceImage);
      log('Face embedding generated: ${embedding.length} dimensions');

      final success = await registerEmployeeEmbedding(embedding);

      String message;
      if (success) {
        message = 'Face registered successfully!';
        _updateState(status: FaceRegistrationStatus.success, message: message);
        await Future.delayed(const Duration(seconds: 3));
        employeeIdController.clear();
      } else {
        message = 'Failed to register face. Please try again.';
        _updateState(status: FaceRegistrationStatus.error, message: message);
        await Future.delayed(const Duration(seconds: 2));
      }
      _resetState();
    } catch (e) {
      await _handleError('An error occurred: $e');
    }
  }

  Future<void> _handleError(String message) async {
    _updateState(status: FaceRegistrationStatus.error, message: message);
    await Future.delayed(const Duration(seconds: 2));
    _resetState();
  }

  void _updateState({
    required FaceRegistrationStatus status,
    required String message,
  }) {
    _status = status;
    _message = message;
    ref.notifyListeners();
  }

  void _resetState() {
    _status = FaceRegistrationStatus.idle;
    _message = null;
    _isProcessing = false;
    _lastProcessTime = null;
    ref.notifyListeners();

    // Resume face detection if needed
    if (_status == FaceRegistrationStatus.processing) {
      _cameraController.startImageStream();
    }
  }

  Future<bool> registerEmployeeEmbedding(List<double> embedding) async {
    final previousState = ref.read(embeddingsProvider);
    ref.read(embeddingsProvider.notifier).state = [...previousState, embedding];

    final score = _cosineSimilarity(embedding, embedding);
    log('Cosine similarity score for registration: $score');

    return true;
  }

  double _cosineSimilarity(List<double> a, List<double> b) {
    if (a.length != b.length) {
      throw Exception('Vectors must be of same length');
    }

    double dot = 0.0;
    double normA = 0.0;
    double normB = 0.0;

    for (int i = 0; i < a.length; i++) {
      dot += a[i] * b[i];
      normA += a[i] * a[i];
      normB += b[i] * b[i];
    }

    return dot / (sqrt(normA) * sqrt(normB));
  }
}
