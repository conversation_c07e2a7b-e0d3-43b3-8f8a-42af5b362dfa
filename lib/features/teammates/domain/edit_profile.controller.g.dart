// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'edit_profile.controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$profileDataHash() => r'63c3c1daae76e8f2b54dc8407779be1c0772381a';

/// See also [ProfileData].
@ProviderFor(ProfileData)
final profileDataProvider =
    AutoDisposeNotifierProvider<ProfileData, UserModel>.internal(
      ProfileData.new,
      name: r'profileDataProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$profileDataHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$ProfileData = AutoDisposeNotifier<UserModel>;
String _$editProfileHash() => r'894e4b9bbdfe565642fdc5519286dd24cdfaf313';

/// See also [EditProfile].
@ProviderFor(EditProfile)
final editProfileProvider =
    AutoDisposeNotifierProvider<EditProfile, EditProfileDataModel>.internal(
      EditProfile.new,
      name: r'editProfileProvider',
      debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
          ? null
          : _$editProfileHash,
      dependencies: null,
      allTransitiveDependencies: null,
    );

typedef _$EditProfile = AutoDisposeNotifier<EditProfileDataModel>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
