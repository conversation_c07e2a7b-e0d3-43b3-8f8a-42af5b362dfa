import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:hrms_tst/core/utils/extensions/context.extension.dart';
import 'package:hrms_tst/features/app_update/domain/app_config.provider.dart';
import 'package:hrms_tst/features/app_update/presentation/app_update.screen.dart';
import 'package:hrms_tst/features/auth/presentation/auth.screen.dart';
import 'package:hrms_tst/features/bottom_navigation/navigation_container.dart';
import 'package:hrms_tst/features/employee/presentation/employee.screen.dart';
import 'package:hrms_tst/features/employee/presentation/leave_policy.screen.dart';
import 'package:hrms_tst/features/employee/presentation/work_policy.screen.dart';
import 'package:hrms_tst/features/face_scanner/presentation/face_recognition_home.screen.dart';
import 'package:hrms_tst/features/face_scanner/presentation/face_registration.screen.dart';
import 'package:hrms_tst/features/face_scanner/presentation/face_scan.screen.dart';
import 'package:hrms_tst/features/home/<USER>/home.screen.dart';
import 'package:hrms_tst/features/home/<USER>/work_update.screen.dart';
import 'package:hrms_tst/features/request/presentation/request.screen.dart';
import 'package:hrms_tst/features/teammates/presentation/edit_profile.screen.dart';
import 'package:hrms_tst/features/teammates/presentation/profile.screen.dart';
import 'package:hrms_tst/features/teammates/presentation/team_progress.screen.dart';
import 'package:hrms_tst/features/teammates/presentation/teammate_detail.screen.dart';
import 'package:hrms_tst/features/teammates/presentation/teammates.screen.dart';
import 'package:hrms_tst/shared/providers/auth.provider.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'features/splash/splash.screen.dart';

part 'router.g.dart';

@riverpod
GoRouter router(Ref ref) {
  final appConfig = ref.watch(appConfigProvider);
  final authStatus = ref.watch(authProvider);
  return GoRouter(
    initialLocation: '/home',
    routes: [
      SplashScreen.route(),
      AppUpdateScreen.route(),
      AuthScreen.route(),
      FaceRecognitionHomeScreen.route(
        routes: [FaceRegistrationScreen.route(), FaceScanScreen.route()],
      ),
      StatefulShellRoute.indexedStack(
        pageBuilder: (context, state, navigationShell) {
          return NoTransitionPage(
            child: NavigationContainer(navigationShell: navigationShell),
          );
        },
        branches: [
          StatefulShellBranch(routes: [HomeScreen.route()]),
          StatefulShellBranch(
            routes: [
              TeammatesScreen.route(
                routes: [
                  TeammateDetailScreen.route(),
                  TeamProgressScreen.route(),
                ],
              ),
            ],
          ),
          StatefulShellBranch(
            routes: [
              EmployeeScreen.route(
                routes: [WorkPolicyScreen.route(), LeavePolicyScreen.route()],
              ),
            ],
          ),
          StatefulShellBranch(routes: [RequestScreen.route()]),
        ],
      ),
      WorkUpdateScreen.route(),
      ProfileScreen.route(),
      EditProfileScreen.route(),
    ],
    redirect: (context, state) {
      SystemChrome.setSystemUIOverlayStyle(
        SystemUiOverlayStyle(
          statusBarColor: context.colors.outline,
          systemNavigationBarColor: context.colors.surface,
          systemNavigationBarDividerColor: context.colors.surface,
        ),
      );
      if (appConfig.isLoading || authStatus.isLoading) return SplashScreen.path;

      if (appConfig.value != null && appConfig.requireValue.isUpdateRequired) {
        return AppUpdateScreen.path;
      }

      bool isAuthenticated = authStatus.requireValue != null;
      bool isAuthenticating = state.matchedLocation == AuthScreen.path;

      return isAuthenticated
          ? isAuthenticating
                ? HomeScreen.path
                : null
          : isAuthenticating
          ? null
          : AuthScreen.path;
    },
    debugLogDiagnostics: false,
  );
}
